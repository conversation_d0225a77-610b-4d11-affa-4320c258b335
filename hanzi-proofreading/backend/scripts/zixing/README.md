# ref为空的反切数据分析脚本

## 概述

本目录包含用于分析yunshu_gy_origin表中ref为空的反切数据的脚本。这些脚本主要用于：

1. 读取反切值进行标准化（补充"切"字）
2. 使用fanqie_mapping.json进行字形转换，转换为标准反切值
3. 按标准反切值分组，标识每组中汉字的不同来源
4. 生成详细的分析报告

## 文件说明

### 核心脚本

- **`analyze_null_ref_fanqie.py`** - 主分析脚本
  - 连接数据库获取ref为空的数据
  - 标准化反切值（补"切"字）
  - 使用字形映射转换反切字符
  - 按标准反切值分组
  - 生成详细的JSON分析结果

- **`generate_summary_report.py`** - 摘要报告生成脚本
  - 从JSON分析结果生成易读的Markdown报告
  - 包含统计分析和问题组识别

### 输出文件

- **`null_ref_fanqie_analysis.json`** - 详细分析结果（JSON格式）
  - 包含所有分组的完整信息
  - 每个汉字的来源、记录详情等

- **`null_ref_fanqie_summary_report.md`** - 摘要报告（Markdown格式）
  - 统计分析
  - 最大反切组
  - 问题组识别

- **`analyze_null_ref_fanqie.log`** - 分析过程日志

## 数据库配置

脚本默认使用以下数据库配置：
- 主机: localhost
- 用户: root
- 密码: 123456
- 数据库: wenlu
- 字符集: utf8mb4

## 依赖要求

```bash
pip install mysql-connector-python
```

## 使用方法

### 1. 运行主分析脚本

```bash
cd /Users/<USER>/Documents/12-汉字/文路/hanzi-proofreading/backend/scripts/zixing
python analyze_null_ref_fanqie.py
```

### 2. 生成摘要报告

```bash
python generate_summary_report.py
```

## 分析结果概览

根据最新分析结果（2025-08-03）：

### 基本统计
- **总记录数**: 5,088条（其中4,809条有反切值，279条为空反切）
- **标准反切组数**: 1,300个
- **包含多源数据的组**: 886组 (68.2%)
- **单汉字组**: 317组 (24.4%)

### 数据源分布
- **qx**: 1,771条记录 (36.8%), 1,641个不同汉字
- **xxt**: 1,753条记录 (36.5%), 1,599个不同汉字  
- **yd**: 1,564条记录 (32.5%), 1,456个不同汉字

### 组大小分布
- 1个汉字: 317组 (24.4%)
- 2个汉字: 639组 (49.2%)
- 3个汉字: 57组 (4.4%)
- 4个汉字: 133组 (10.2%)
- 其他: 154组 (11.8%)

### 最大的反切组
1. **[空反切]**: 268个汉字, 279条记录
2. **職鄰切**: 18个汉字, 32条记录
3. **則旰切**: 18个汉字, 27条记录
4. **祖稽切**: 17个汉字, 30条记录
5. **徒候切**: 16个汉字, 32条记录

## 字形映射

脚本使用`../merge/fanqie_mapping.json`文件进行字形转换，包含16个映射关系：

```json
{
  "𡛷": "姊",
  "姉": "姊", 
  "吕": "呂",
  "𡋡": "袁",
  "𦵔": "葅",
  "虚": "虛",
  "𥳑": "簡",
  "𨦣": "銳",
  "𢈔": "庾",
  "遟": "遲",
  "内": "內",
  "𢌿": "畀",
  "𥬇": "笑",
  "𤑔": "爇",
  "乗": "乘",
  "𡝤": "婁"
}
```

## 反切标准化规则

1. **补"切"字**: 如果反切值不以"切"字结尾，自动补充"切"字
2. **字形转换**: 使用映射文件将异体字转换为标准字形
3. **空值处理**: 将fan_qie为空或NULL的记录归入"[空反切]"组

## 分析发现

### 主要问题

1. **空反切组**: 268个汉字缺少反切信息，主要来源于qx数据源
2. **字形差异**: 通过映射转换发现了多个字形变体关系
3. **多源一致性**: 68.2%的组包含多源数据，显示了数据的一致性

### 转换统计示例

最常见的反切转换：
- `黃（練）〔絢〕 -> 黃（練）〔絢〕切`: 15次
- `美（畢）〔筆〕 -> 美（畢）〔筆〕切`: 10次  
- `盧𣊟 -> 盧𣊟切`: 9次
- `吕（䘏）〔卹〕 -> 呂（䘏）〔卹〕切`: 8次

## 技术特点

1. **批量处理**: 支持大规模数据处理
2. **字形标准化**: 自动进行字形转换
3. **多源整合**: 识别和标记不同数据源
4. **详细日志**: 完整的处理过程记录
5. **结构化输出**: JSON和Markdown双格式输出

## 后续工作建议

1. **扩展字形映射**: 根据分析结果补充更多字形映射关系
2. **空反切处理**: 研究空反切组中汉字的特点，寻找补充方案
3. **数据质量评估**: 基于多源一致性评估数据质量
4. **自动化流程**: 将分析流程集成到数据处理管道中

## 注意事项

1. 确保数据库连接正常
2. fanqie_mapping.json文件路径正确
3. 有足够的磁盘空间存储分析结果
4. 分析过程可能需要几分钟时间

## 版本历史

- **v1.0** (2025-08-03): 初始版本，完成基本分析功能
