#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分析yunshu_gy_origin表中ref为空的反切数据
1. 读取反切值进行标准化，没有以"切"字结尾的补充"切"字
2. 用fanqie_mapping.json进行字形转换，转换为标准反切值
3. 用标准反切值分组，每组里标识汉字的不同来源
4. 保存到JSON文件中

作者: AI Assistant
日期: 2025-08-03
"""

import os
import sys
import json
import logging
from datetime import datetime
from collections import defaultdict
from typing import Dict, List, Optional, Tuple
import mysql.connector
from mysql.connector import Error

# 添加项目根目录到Python路径
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '../../..'))
sys.path.insert(0, project_root)

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('analyze_null_ref_fanqie.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class NullRefFanqieAnalyzer:
    """分析ref为空的反切数据"""
    
    def __init__(self, db_config: Dict[str, str]):
        """初始化分析器
        
        Args:
            db_config: 数据库配置
        """
        self.db_config = db_config
        self.connection = None
        self.fanqie_mapping = {}
        self.processed_count = 0
        self.total_count = 0
        
    def connect_database(self) -> bool:
        """连接数据库"""
        try:
            self.connection = mysql.connector.connect(**self.db_config)
            logger.info("数据库连接成功")
            return True
        except Error as e:
            logger.error(f"数据库连接失败: {e}")
            return False
    
    def close_database(self):
        """关闭数据库连接"""
        if self.connection and self.connection.is_connected():
            self.connection.close()
            logger.info("数据库连接已关闭")
    
    def load_fanqie_mapping(self, mapping_file: str) -> bool:
        """加载反切字形映射文件
        
        Args:
            mapping_file: 映射文件路径
            
        Returns:
            bool: 是否加载成功
        """
        try:
            if os.path.exists(mapping_file):
                with open(mapping_file, 'r', encoding='utf-8') as f:
                    self.fanqie_mapping = json.load(f)
                logger.info(f"成功加载反切映射文件: {mapping_file}, 包含 {len(self.fanqie_mapping)} 个映射")
            else:
                logger.warning(f"反切映射文件不存在: {mapping_file}")
                self.fanqie_mapping = {}
            return True
        except Exception as e:
            logger.error(f"加载反切映射文件失败: {e}")
            return False
    
    def normalize_fanqie(self, fanqie: str) -> str:
        """标准化反切值，补充"切"字
        
        Args:
            fanqie: 原始反切值
            
        Returns:
            str: 标准化后的反切值
        """
        if not fanqie:
            return fanqie
        
        # 去除首尾空白
        fanqie = fanqie.strip()
        
        # 如果不以"切"字结尾，则补充"切"字
        if fanqie and not fanqie.endswith('切'):
            fanqie += '切'
        
        return fanqie
    
    def convert_fanqie_characters(self, fanqie: str) -> str:
        """使用映射转换反切值中的字符
        
        Args:
            fanqie: 反切值
            
        Returns:
            str: 转换后的反切值
        """
        if not fanqie or not self.fanqie_mapping:
            return fanqie
        
        converted = fanqie
        for old_char, new_char in self.fanqie_mapping.items():
            if old_char in converted:
                converted = converted.replace(old_char, new_char)
        
        return converted
    
    def process_fanqie(self, fanqie: str) -> str:
        """处理反切值：标准化 + 字形转换
        
        Args:
            fanqie: 原始反切值
            
        Returns:
            str: 处理后的标准反切值
        """
        # 1. 标准化（补"切"字）
        normalized = self.normalize_fanqie(fanqie)
        
        # 2. 字形转换
        converted = self.convert_fanqie_characters(normalized)
        
        return converted
    
    def get_null_ref_data(self, batch_size: int = 1000) -> List[Dict]:
        """获取ref为空的数据
        
        Args:
            batch_size: 批处理大小
            
        Returns:
            List[Dict]: 数据记录列表
        """
        if not self.connection:
            logger.error("数据库未连接")
            return []
        
        try:
            cursor = self.connection.cursor(dictionary=True)
            
            # 先获取总数
            count_query = "SELECT COUNT(*) as total FROM yunshu_gy_origin WHERE ref IS NULL"
            cursor.execute(count_query)
            self.total_count = cursor.fetchone()['total']
            logger.info(f"找到 {self.total_count} 条ref为空的记录")
            
            # 分批获取数据
            query = """
            SELECT id, unicode, hanzi, source, fan_qie, sheng_mu, yun_bu, 
                   sheng_diao, kai_he, deng_di, she, xiao_yun, qing_zhuo, shi_yi
            FROM yunshu_gy_origin 
            WHERE ref IS NULL 
            ORDER BY unicode, source
            """
            
            cursor.execute(query)
            records = cursor.fetchall()
            cursor.close()
            
            logger.info(f"成功获取 {len(records)} 条记录")
            return records
            
        except Error as e:
            logger.error(f"获取数据失败: {e}")
            return []
    
    def group_by_standard_fanqie(self, records: List[Dict]) -> Dict[str, List[Dict]]:
        """按标准反切值分组
        
        Args:
            records: 数据记录列表
            
        Returns:
            Dict[str, List[Dict]]: 按标准反切值分组的数据
        """
        groups = defaultdict(list)
        conversion_stats = defaultdict(int)
        
        for record in records:
            original_fanqie = record.get('fan_qie', '')
            if not original_fanqie:
                # 处理空反切值
                groups['[空反切]'].append(record)
                continue
            
            # 处理反切值
            standard_fanqie = self.process_fanqie(original_fanqie)
            
            # 记录转换统计
            if standard_fanqie != original_fanqie:
                conversion_stats[f"{original_fanqie} -> {standard_fanqie}"] += 1
            
            # 添加处理后的反切值到记录中
            record['standard_fanqie'] = standard_fanqie
            record['original_fanqie'] = original_fanqie
            
            groups[standard_fanqie].append(record)
            self.processed_count += 1
        
        # 输出转换统计
        if conversion_stats:
            logger.info(f"反切转换统计（前10项）:")
            for conversion, count in sorted(conversion_stats.items(), key=lambda x: x[1], reverse=True)[:10]:
                logger.info(f"  {conversion}: {count}次")
        
        logger.info(f"处理完成，共 {len(groups)} 个不同的标准反切值")
        return dict(groups)
    
    def analyze_group_sources(self, group_data: Dict[str, List[Dict]]) -> Dict:
        """分析每组中汉字的不同来源
        
        Args:
            group_data: 按反切分组的数据
            
        Returns:
            Dict: 分析结果
        """
        analysis_result = {
            'metadata': {
                'generated_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                'description': 'ref为空的反切数据分析结果',
                'total_groups': len(group_data),
                'total_records': self.processed_count,
                'version': '1.0'
            },
            'groups': {}
        }
        
        for standard_fanqie, records in group_data.items():
            # 按unicode分组，统计每个汉字的来源
            unicode_groups = defaultdict(lambda: {
                'hanzi': '',
                'sources': set(),
                'records': []
            })
            
            for record in records:
                unicode_code = record['unicode']
                unicode_groups[unicode_code]['hanzi'] = record['hanzi']
                unicode_groups[unicode_code]['sources'].add(record['source'])
                unicode_groups[unicode_code]['records'].append({
                    'id': record['id'],
                    'source': record['source'],
                    'original_fanqie': record.get('original_fanqie', ''),
                    'sheng_mu': record.get('sheng_mu', ''),
                    'yun_bu': record.get('yun_bu', ''),
                    'sheng_diao': record.get('sheng_diao', ''),
                    'shi_yi': record.get('shi_yi', '')
                })
            
            # 转换为最终格式
            group_info = {
                'standard_fanqie': standard_fanqie,
                'total_hanzi_count': len(unicode_groups),
                'total_record_count': len(records),
                'hanzi_details': []
            }
            
            for unicode_code, info in unicode_groups.items():
                hanzi_info = {
                    'unicode': unicode_code,
                    'hanzi': info['hanzi'],
                    'sources': sorted(list(info['sources'])),
                    'source_count': len(info['sources']),
                    'record_count': len(info['records']),
                    'records': info['records']
                }
                group_info['hanzi_details'].append(hanzi_info)
            
            # 按汉字排序
            group_info['hanzi_details'].sort(key=lambda x: x['unicode'])
            
            analysis_result['groups'][standard_fanqie] = group_info
        
        return analysis_result

    def save_analysis_result(self, result: Dict, output_file: str) -> bool:
        """保存分析结果到JSON文件

        Args:
            result: 分析结果
            output_file: 输出文件路径

        Returns:
            bool: 是否保存成功
        """
        try:
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(result, f, ensure_ascii=False, indent=2)
            logger.info(f"分析结果已保存到: {output_file}")
            return True
        except Exception as e:
            logger.error(f"保存分析结果失败: {e}")
            return False

    def generate_summary_report(self, result: Dict) -> str:
        """生成摘要报告

        Args:
            result: 分析结果

        Returns:
            str: 摘要报告
        """
        metadata = result['metadata']
        groups = result['groups']

        # 统计信息
        total_groups = metadata['total_groups']
        total_records = metadata['total_records']

        # 按组大小统计
        group_sizes = defaultdict(int)
        multi_source_groups = 0
        single_char_groups = 0

        for fanqie, group_info in groups.items():
            hanzi_count = group_info['total_hanzi_count']
            record_count = group_info['total_record_count']

            group_sizes[hanzi_count] += 1

            if hanzi_count == 1:
                single_char_groups += 1

            # 检查是否有多源数据
            has_multi_source = any(
                hanzi['source_count'] > 1
                for hanzi in group_info['hanzi_details']
            )
            if has_multi_source:
                multi_source_groups += 1

        # 生成报告
        report = f"""
=== ref为空的反切数据分析摘要报告 ===
生成时间: {metadata['generated_time']}

基本统计:
- 总记录数: {total_records:,}
- 标准反切组数: {total_groups:,}
- 包含多源数据的组: {multi_source_groups:,}
- 单汉字组: {single_char_groups:,}

按组内汉字数量分布:"""

        for size in sorted(group_sizes.keys()):
            count = group_sizes[size]
            percentage = (count / total_groups) * 100
            report += f"\n- {size}个汉字: {count:,}组 ({percentage:.1f}%)"

        # 找出最大的几个组
        largest_groups = sorted(
            groups.items(),
            key=lambda x: x[1]['total_hanzi_count'],
            reverse=True
        )[:5]

        report += f"\n\n最大的5个反切组:"
        for fanqie, group_info in largest_groups:
            hanzi_count = group_info['total_hanzi_count']
            record_count = group_info['total_record_count']
            report += f"\n- '{fanqie}': {hanzi_count}个汉字, {record_count}条记录"

        return report

    def run_analysis(self, mapping_file: str, output_file: str) -> bool:
        """运行完整分析流程

        Args:
            mapping_file: 反切映射文件路径
            output_file: 输出文件路径

        Returns:
            bool: 是否成功
        """
        logger.info("开始分析ref为空的反切数据")

        try:
            # 1. 连接数据库
            if not self.connect_database():
                return False

            # 2. 加载反切映射
            if not self.load_fanqie_mapping(mapping_file):
                return False

            # 3. 获取数据
            logger.info("正在获取ref为空的数据...")
            records = self.get_null_ref_data()
            if not records:
                logger.error("未获取到数据")
                return False

            # 4. 按标准反切分组
            logger.info("正在按标准反切值分组...")
            group_data = self.group_by_standard_fanqie(records)

            # 5. 分析每组的来源信息
            logger.info("正在分析每组的来源信息...")
            analysis_result = self.analyze_group_sources(group_data)

            # 6. 保存结果
            if not self.save_analysis_result(analysis_result, output_file):
                return False

            # 7. 生成并显示摘要报告
            summary = self.generate_summary_report(analysis_result)
            logger.info(summary)

            logger.info("分析完成！")
            return True

        except Exception as e:
            logger.error(f"分析过程中发生错误: {e}")
            return False
        finally:
            self.close_database()


def main():
    """主函数"""
    # 数据库配置
    db_config = {
        'host': 'localhost',
        'user': 'root',
        'password': '123456',
        'database': 'wenlu',
        'charset': 'utf8mb4'
    }

    # 文件路径配置
    script_dir = os.path.dirname(os.path.abspath(__file__))
    mapping_file = os.path.join(script_dir, '../merge/fanqie_mapping.json')
    output_file = os.path.join(script_dir, 'null_ref_fanqie_analysis.json')

    # 检查映射文件是否存在
    if not os.path.exists(mapping_file):
        # 尝试其他可能的位置
        alternative_paths = [
            os.path.join(script_dir, '../resolve/fanqie_mapping.json'),
            os.path.join(script_dir, 'fanqie_mapping.json')
        ]

        for alt_path in alternative_paths:
            if os.path.exists(alt_path):
                mapping_file = alt_path
                break
        else:
            logger.warning(f"未找到反切映射文件，将使用空映射")
            mapping_file = None

    # 创建分析器并运行
    analyzer = NullRefFanqieAnalyzer(db_config)

    try:
        success = analyzer.run_analysis(mapping_file, output_file)
        if success:
            print(f"\n✅ 分析完成！结果已保存到: {output_file}")
        else:
            print("\n❌ 分析失败，请查看日志了解详情")
            sys.exit(1)
    except KeyboardInterrupt:
        print("\n⚠️  用户中断操作")
        sys.exit(1)
    except Exception as e:
        logger.error(f"程序执行失败: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
