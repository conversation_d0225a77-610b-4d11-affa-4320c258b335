#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
生成ref为空的反切数据分析摘要报告
从null_ref_fanqie_analysis.json生成易读的摘要报告

作者: AI Assistant
日期: 2025-08-03
"""

import json
import os
from collections import defaultdict
from typing import Dict, List

def load_analysis_data(file_path: str) -> Dict:
    """加载分析数据"""
    with open(file_path, 'r', encoding='utf-8') as f:
        return json.load(f)

def generate_detailed_summary(data: Dict) -> str:
    """生成详细摘要报告"""
    metadata = data['metadata']
    groups = data['groups']
    
    report = f"""
# ref为空的反切数据分析详细报告

## 基本信息
- 生成时间: {metadata['generated_time']}
- 总记录数: {metadata['total_records']:,}
- 标准反切组数: {metadata['total_groups']:,}
- 版本: {metadata['version']}

## 统计分析

### 1. 按组内汉字数量分布
"""
    
    # 统计组大小分布
    group_sizes = defaultdict(int)
    multi_source_groups = 0
    single_char_groups = 0
    
    for fanqie, group_info in groups.items():
        hanzi_count = group_info['total_hanzi_count']
        group_sizes[hanzi_count] += 1
        
        if hanzi_count == 1:
            single_char_groups += 1
        
        # 检查是否有多源数据
        has_multi_source = any(
            hanzi['source_count'] > 1 
            for hanzi in group_info['hanzi_details']
        )
        if has_multi_source:
            multi_source_groups += 1
    
    total_groups = len(groups)
    
    for size in sorted(group_sizes.keys()):
        count = group_sizes[size]
        percentage = (count / total_groups) * 100
        report += f"- {size}个汉字: {count:,}组 ({percentage:.1f}%)\n"
    
    report += f"""
### 2. 多源数据统计
- 包含多源数据的组: {multi_source_groups:,}组 ({(multi_source_groups/total_groups)*100:.1f}%)
- 单汉字组: {single_char_groups:,}组 ({(single_char_groups/total_groups)*100:.1f}%)

### 3. 最大的反切组（前10个）
"""
    
    # 找出最大的组
    largest_groups = sorted(
        groups.items(),
        key=lambda x: x[1]['total_hanzi_count'],
        reverse=True
    )[:10]
    
    for i, (fanqie, group_info) in enumerate(largest_groups, 1):
        hanzi_count = group_info['total_hanzi_count']
        record_count = group_info['total_record_count']
        
        # 统计来源分布
        source_stats = defaultdict(int)
        for hanzi in group_info['hanzi_details']:
            for source in hanzi['sources']:
                source_stats[source] += 1
        
        source_info = ', '.join([f"{src}:{count}" for src, count in sorted(source_stats.items())])
        
        report += f"{i:2d}. '{fanqie}': {hanzi_count}个汉字, {record_count}条记录 (来源: {source_info})\n"
    
    report += """
### 4. 数据源分布统计
"""
    
    # 统计各数据源的记录数
    source_stats = defaultdict(int)
    source_hanzi_stats = defaultdict(set)
    
    for fanqie, group_info in groups.items():
        for hanzi in group_info['hanzi_details']:
            unicode_code = hanzi['unicode']
            for record in hanzi['records']:
                source = record['source']
                source_stats[source] += 1
                source_hanzi_stats[source].add(unicode_code)
    
    for source in sorted(source_stats.keys()):
        record_count = source_stats[source]
        hanzi_count = len(source_hanzi_stats[source])
        percentage = (record_count / metadata['total_records']) * 100
        report += f"- {source}: {record_count:,}条记录 ({percentage:.1f}%), {hanzi_count:,}个不同汉字\n"
    
    return report

def generate_problematic_groups_report(data: Dict) -> str:
    """生成问题组报告"""
    groups = data['groups']
    
    report = """
## 需要关注的问题组

### 1. 空反切组
"""
    
    empty_fanqie_group = groups.get('[空反切]')
    if empty_fanqie_group:
        hanzi_count = empty_fanqie_group['total_hanzi_count']
        record_count = empty_fanqie_group['total_record_count']
        
        report += f"- 汉字数量: {hanzi_count}\n"
        report += f"- 记录数量: {record_count}\n"
        
        # 统计来源分布
        source_stats = defaultdict(int)
        for hanzi in empty_fanqie_group['hanzi_details']:
            for source in hanzi['sources']:
                source_stats[source] += 1
        
        report += "- 来源分布: " + ', '.join([f"{src}:{count}" for src, count in sorted(source_stats.items())]) + "\n"
        
        # 显示前10个汉字
        report += "- 前10个汉字示例:\n"
        for i, hanzi in enumerate(empty_fanqie_group['hanzi_details'][:10], 1):
            sources = ', '.join(hanzi['sources'])
            report += f"  {i:2d}. {hanzi['hanzi']} (U+{hanzi['unicode']}) - 来源: {sources}\n"
    
    report += """
### 2. 大型反切组（超过10个汉字）
"""
    
    large_groups = [
        (fanqie, group_info) for fanqie, group_info in groups.items()
        if group_info['total_hanzi_count'] > 10 and fanqie != '[空反切]'
    ]
    
    large_groups.sort(key=lambda x: x[1]['total_hanzi_count'], reverse=True)
    
    for fanqie, group_info in large_groups:
        hanzi_count = group_info['total_hanzi_count']
        record_count = group_info['total_record_count']
        
        # 统计来源分布
        source_stats = defaultdict(int)
        multi_source_hanzi = 0
        
        for hanzi in group_info['hanzi_details']:
            if hanzi['source_count'] > 1:
                multi_source_hanzi += 1
            for source in hanzi['sources']:
                source_stats[source] += 1
        
        source_info = ', '.join([f"{src}:{count}" for src, count in sorted(source_stats.items())])
        
        report += f"- '{fanqie}': {hanzi_count}个汉字, {record_count}条记录\n"
        report += f"  来源分布: {source_info}\n"
        report += f"  多源汉字: {multi_source_hanzi}个\n"
        
        # 显示部分汉字
        sample_hanzi = [h['hanzi'] for h in group_info['hanzi_details'][:10]]
        report += f"  汉字示例: {''.join(sample_hanzi)}\n\n"
    
    return report

def main():
    """主函数"""
    script_dir = os.path.dirname(os.path.abspath(__file__))
    input_file = os.path.join(script_dir, 'null_ref_fanqie_analysis.json')
    output_file = os.path.join(script_dir, 'null_ref_fanqie_summary_report.md')
    
    if not os.path.exists(input_file):
        print(f"❌ 分析数据文件不存在: {input_file}")
        return
    
    try:
        # 加载数据
        print("正在加载分析数据...")
        data = load_analysis_data(input_file)
        
        # 生成报告
        print("正在生成摘要报告...")
        report = generate_detailed_summary(data)
        report += generate_problematic_groups_report(data)
        
        # 保存报告
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(report)
        
        print(f"✅ 摘要报告已生成: {output_file}")
        
        # 显示部分报告内容
        print("\n" + "="*50)
        print("报告预览:")
        print("="*50)
        lines = report.split('\n')
        for line in lines[:30]:  # 显示前30行
            print(line)
        
        if len(lines) > 30:
            print(f"\n... (还有 {len(lines)-30} 行，请查看完整报告文件)")
        
    except Exception as e:
        print(f"❌ 生成报告失败: {e}")

if __name__ == "__main__":
    main()
